import pytest
import asyncio
import datetime
from unittest.mock import Mock, patch, AsyncMock
import pandas as pd
from exchange.runners.redis_data_handler import Redis<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ner
from toti.rpc import datasnapservice_old_pb2 as datasnap_service_pb2


@pytest.fixture
def mock_redis_client():
    """Fixture to provide a mocked Redis client."""
    mock_client = AsyncMock()
    mock_pipeline = AsyncMock()
    mock_client.pipeline.return_value.__aenter__.return_value = mock_pipeline
    mock_client.pipeline.return_value.__aexit__.return_value = None
    return mock_client


@pytest.fixture
def mock_datasnap_client():
    """Fixture to provide a mocked DataSnap client."""
    mock_client = Mock()
    mock_client.get_latest_second = Mock()
    mock_client.get_tick_data = Mock()
    mock_client.close = Mock()
    return mock_client


@pytest.fixture
def mock_balte_initializer():
    """Fixture to mock balte initializer."""
    patch("balte.utility_inbuilt.balte_initializer", return_value=None)


@pytest.fixture
def mock_balte_config():
    """Fixture to provide mocked balte config."""
    with patch('balte.balte_config') as mock_config:
        mock_config.symbol_to_balte_id = {
            'NIFTY': 1234,
            'BANKNIFTY': 5678,
            'GOLD': 9999
        }
        mock_config.MARKET_CLOSE_HOUR = 15
        mock_config.MARKET_CLOSE_MINUTE = 30
        mock_config.TIMEZONE = 'Asia/Kolkata'
        yield mock_config


@pytest.fixture
def redis_data_handler(mock_datasnap_client, mock_balte_config, mock_balte_initializer):
    """Fixture to provide a RedisDataHandlerRunner instance with mocked dependencies."""
    with patch('exchange.runners.redis_data_handler.DataSnapClient') as mock_datasnap_class:
        mock_datasnap_class.return_value = mock_datasnap_client
        handler = RedisDataHandlerRunner()
        handler.datasnap_client = mock_datasnap_client
        return handler


class TestRedisDataHandlerRunnerInitialization:
    """Test cases for RedisDataHandlerRunner initialization."""

    def test_init_sets_redis_connection_params(self, redis_data_handler):
        """Test that initialization sets Redis connection parameters."""
        assert redis_data_handler.redis_host == 'localhost'
        assert redis_data_handler.redis_port == 6379
        assert redis_data_handler.redis_db == ""
        assert redis_data_handler.redis_client is None

    def test_init_creates_datasnap_client(self, redis_data_handler, mock_datasnap_client):
        """Test that initialization creates a DataSnap client."""
        assert redis_data_handler.datasnap_client == mock_datasnap_client

    def test_init_sets_market_closing_time(self, redis_data_handler, mock_balte_config):
        """Test that initialization sets market closing time correctly."""
        expected_time = redis_data_handler.get_local_timestamp().normalize().replace(
            hour=15, minute=30
        )
        assert redis_data_handler.market_closing_time.hour == expected_time.hour
        assert redis_data_handler.market_closing_time.minute == expected_time.minute

    def test_init_creates_future_for_market_closed(self, redis_data_handler):
        """Test that initialization creates a future for market closed flag."""
        assert isinstance(redis_data_handler.is_market_closed, asyncio.Future)
        assert not redis_data_handler.is_market_closed.done()

    def test_init_creates_empty_cache(self, redis_data_handler):
        """Test that initialization creates an empty contract cache."""
        assert redis_data_handler.contract_to_balte_id_cache == {}


class TestRedisDataHandlerRunnerStaticMethods:
    """Test cases for static methods."""

    def test_get_local_timestamp_returns_timestamp(self, mock_balte_config):
        """Test that get_local_timestamp returns a pandas Timestamp."""
        with patch('pandas.Timestamp.now') as mock_now:
            mock_timestamp = pd.Timestamp('2023-01-01 10:00:00')
            mock_now.return_value.replace.return_value = mock_timestamp
            
            result = RedisDataHandlerRunner.get_local_timestamp()
            
            mock_now.assert_called_once_with(tz=mock_balte_config.TIMEZONE)
            assert result == mock_timestamp

    def test_parse_contract_with_options(self):
        """Test parsing contract with options."""
        contract = "NIFTY31-Jul-2025CE24000"
        symbol, expiry, option_type, strike = RedisDataHandlerRunner.parse_contract(contract)
        
        assert symbol == "NIFTY"
        assert expiry == "31-Jul-2025"
        assert option_type == "CE"
        assert strike == "24000"

    def test_parse_contract_with_futures(self):
        """Test parsing contract with futures."""
        contract = "GOLD31-Dec-2023"
        symbol, expiry, option_type, strike = RedisDataHandlerRunner.parse_contract(contract)
        
        assert symbol == "GOLD"
        assert expiry == "31-Dec-2023"
        assert option_type == ""
        assert strike == ""

    def test_parse_contract_spot_only(self):
        """Test parsing contract with spot only."""
        contract = "NIFTY"
        symbol, expiry, option_type, strike = RedisDataHandlerRunner.parse_contract(contract)
        
        assert symbol == "NIFTY"
        assert expiry == ""
        assert option_type == ""
        assert strike == ""

    def test_contract_name_to_balte_id_spot(self, mock_balte_config):
        """Test contract_name_to_balte_id for spot contracts."""
        result = RedisDataHandlerRunner.contract_name_to_balte_id("mcx_spot", "NIFTY")
        assert result == 1234

    def test_contract_name_to_balte_id_futures(self, mock_balte_config):
        """Test contract_name_to_balte_id for futures contracts."""
        result = RedisDataHandlerRunner.contract_name_to_balte_id(
            "mcx_fut_near", "GOLD31-Dec-2023"
        )
        expected = int("2023123121234")  # expiry_code + type_code + underlying_code
        assert result == expected

    def test_contract_name_to_balte_id_options_optcom(self, mock_balte_config):
        """Test contract_name_to_balte_id for optcom options."""
        result = RedisDataHandlerRunner.contract_name_to_balte_id(
            "optcom", "NIFTY31-Dec-2023CE24000"
        )
        # strike//10 + expiry_code + type_code + underlying_code
        expected = int("240020231231" + "1" + "1234")
        assert result == expected


class TestRedisDataHandlerRunnerCaching:
    """Test cases for caching functionality."""

    def test_get_balte_id_caches_result(self, redis_data_handler, mock_balte_config):
        """Test that get_balte_id caches the result."""
        contract = "NIFTY"
        
        # First call
        result1 = redis_data_handler.get_balte_id(contract)
        assert result1 == 1234
        assert redis_data_handler.contract_to_balte_id_cache[contract] == 1234
        
        # Second call should use cache
        result2 = redis_data_handler.get_balte_id(contract)
        assert result2 == 1234

    def test_clear_balte_id_cache(self, redis_data_handler):
        """Test clearing the balte_id cache."""
        redis_data_handler.contract_to_balte_id_cache["TEST"] = 123
        redis_data_handler.clear_balte_id_cache()
        assert redis_data_handler.contract_to_balte_id_cache == {}


class TestRedisDataHandlerRunnerRedisOperations:
    """Test cases for Redis operations."""

    @pytest.mark.asyncio
    async def test_connect_to_redis_creates_client(self, redis_data_handler):
        """Test that connect_to_redis creates a Redis client."""
        with patch('redis.asyncio.from_url') as mock_from_url:
            mock_client = AsyncMock()
            mock_from_url.return_value = mock_client
            
            await redis_data_handler.connect_to_redis()
            
            mock_from_url.assert_called_once_with(
                "redis://localhost:6379", decode_responses=True
            )
            assert redis_data_handler.redis_client == mock_client

    @pytest.mark.asyncio
    async def test_connect_to_redis_skips_if_already_connected(self, redis_data_handler):
        """Test that connect_to_redis skips if already connected."""
        mock_client = AsyncMock()
        redis_data_handler.redis_client = mock_client
        
        with patch('redis.asyncio.from_url') as mock_from_url:
            await redis_data_handler.connect_to_redis()
            mock_from_url.assert_not_called()

    @pytest.mark.asyncio
    async def test_store_balte_id_to_ltp_in_redis_success(self, redis_data_handler, mock_redis_client, mock_balte_config):
        """Test successful storage of balte_id to LTP mappings."""
        redis_data_handler.redis_client = mock_redis_client
        tick_data = [
            "1|NIFTY|field2|100.50|field4",
            "2|BANKNIFTY|field2|200.75|field4"
        ]
        
        mock_pipeline = AsyncMock()
        mock_redis_client.pipeline.return_value.__aenter__.return_value = mock_pipeline
        
        await redis_data_handler.store_balte_id_to_ltp_in_redis(tick_data)
        
        # Verify pipeline operations
        assert mock_pipeline.set.call_count == 2
        mock_pipeline.set.assert_any_call("1234", "100.50")  # NIFTY balte_id
        mock_pipeline.set.assert_any_call("5678", "200.75")  # BANKNIFTY balte_id
        mock_pipeline.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_store_balte_id_to_ltp_raises_if_no_client(self, redis_data_handler):
        """Test that store_balte_id_to_ltp raises exception if Redis client not initialized."""
        tick_data = ["1|NIFTY|field2|100.50|field4"]
        
        with pytest.raises(Exception, match="Redis client is not initialized"):
            await redis_data_handler.store_balte_id_to_ltp_in_redis(tick_data)


class TestRedisDataHandlerRunnerMarketOperations:
    """Test cases for market operations."""

    @pytest.mark.asyncio
    async def test_market_termination_checker_sets_flag(self, redis_data_handler):
        """Test that market_termination_checker sets the market closed flag."""
        # Mock the market closing time to be very soon
        redis_data_handler.market_closing_time = redis_data_handler.get_local_timestamp() + pd.Timedelta(seconds=0.1)

        await redis_data_handler.market_termination_checker()

        assert redis_data_handler.is_market_closed.done()
        assert redis_data_handler.is_market_closed.result() is True

    @pytest.mark.asyncio
    async def test_fetch_and_store_data_success(self, redis_data_handler, mock_datasnap_client, mock_redis_client):
        """Test successful fetch and store data operation."""
        # Setup mocks
        redis_data_handler.redis_client = mock_redis_client
        redis_data_handler.is_market_closed.set_result(True)  # Stop the loop immediately

        # Mock datasnap responses
        timestamp_response = datasnap_service_pb2.TimestampResponse()
        timestamp_response.timestamp = "2023-01-01T10:00:00"
        mock_datasnap_client.get_latest_second.return_value = timestamp_response

        tick_response = datasnap_service_pb2.RepeatedArrayReply()
        tick_response.message.extend(["1|NIFTY|field2|100.50|field4"])
        mock_datasnap_client.get_tick_data.return_value = tick_response

        # Mock pipeline
        mock_pipeline = AsyncMock()
        mock_redis_client.pipeline.return_value.__aenter__.return_value = mock_pipeline

        with patch.object(redis_data_handler, 'connect_to_redis') as mock_connect:
            await redis_data_handler.fetch_and_store_data()

            mock_connect.assert_called()
            mock_datasnap_client.get_latest_second.assert_called_with(segment="MCX")
            mock_datasnap_client.get_tick_data.assert_called_with(
                segment="MCX", timestamp="2023-01-01T10:00:00"
            )
            mock_datasnap_client.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_fetch_and_store_data_handles_exception(self, redis_data_handler, mock_datasnap_client):
        """Test that fetch_and_store_data handles exceptions gracefully."""
        redis_data_handler.is_market_closed.set_result(True)  # Stop the loop immediately
        mock_datasnap_client.get_latest_second.side_effect = Exception("Connection error")

        with patch('builtins.print') as mock_print, \
             patch.object(redis_data_handler, 'connect_to_redis'):
            await redis_data_handler.fetch_and_store_data()

            mock_print.assert_called_with("Error occurred: Connection error")
            mock_datasnap_client.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_redis_data_client_runner(self, redis_data_handler):
        """Test the main runner method."""
        with patch.object(redis_data_handler, 'fetch_and_store_data') as mock_fetch, \
             patch.object(redis_data_handler, 'market_termination_checker') as mock_termination:
            mock_fetch.return_value = None
            mock_termination.return_value = None

            await redis_data_handler.redis_data_client_runner()

            mock_fetch.assert_called_once()
            mock_termination.assert_called_once()

    def test_run_method(self, redis_data_handler):
        """Test the synchronous run method."""
        with patch('asyncio.run') as mock_asyncio_run:
            redis_data_handler.run()
            mock_asyncio_run.assert_called_once_with(redis_data_handler.redis_data_client_runner())


class TestRedisDataHandlerRunnerErrorHandling:
    """Test cases for error handling."""

    def test_contract_name_to_balte_id_invalid_option_type(self, mock_balte_config):
        """Test contract_name_to_balte_id with invalid option type."""
        with pytest.raises(ValueError, match="Invalid option type: 'XX'"):
            RedisDataHandlerRunner.contract_name_to_balte_id(
                "optcom", "NIFTY31-Dec-2023XX24000"
            )

    def test_contract_name_to_balte_id_missing_strike_for_options(self, mock_balte_config):
        """Test contract_name_to_balte_id with missing strike for options."""
        with pytest.raises(ValueError, match="CE or PE type contract has no strike"):
            RedisDataHandlerRunner.contract_name_to_balte_id(
                "optcom", "NIFTY31-Dec-2023CE"
            )

    def test_contract_name_to_balte_id_unsupported_universe(self, mock_balte_config):
        """Test contract_name_to_balte_id with unsupported opt universe."""
        with pytest.raises(NotImplementedError, match="not implemented for this opt universe"):
            RedisDataHandlerRunner.contract_name_to_balte_id(
                "opt_unsupported", "NIFTY31-Dec-2023CE24000"
            )

    @pytest.mark.asyncio
    async def test_store_balte_id_skips_invalid_contracts(self, redis_data_handler, mock_redis_client, mock_balte_config):
        """Test that store_balte_id_to_ltp skips contracts with invalid balte_id."""
        redis_data_handler.redis_client = mock_redis_client

        # Mock get_balte_id to return -1 for invalid contract
        with patch.object(redis_data_handler, 'get_balte_id', return_value=-1):
            tick_data = ["1|INVALID|field2|100.50|field4"]

            mock_pipeline = AsyncMock()
            mock_redis_client.pipeline.return_value.__aenter__.return_value = mock_pipeline

            await redis_data_handler.store_balte_id_to_ltp_in_redis(tick_data)

            # Should not call set for invalid contracts
            mock_pipeline.set.assert_not_called()
            mock_pipeline.execute.assert_called_once()


class TestRedisDataHandlerRunnerEdgeCases:
    """Test cases for edge cases and boundary conditions."""

    def test_get_balte_id_with_different_contract_types(self, redis_data_handler, mock_balte_config):
        """Test get_balte_id with different contract types."""
        # Test option contract
        option_contract = "NIFTY31-Dec-2023CE24000"
        with patch.object(redis_data_handler, 'contract_name_to_balte_id', return_value=12345) as mock_method:
            result = redis_data_handler.get_balte_id(option_contract)
            mock_method.assert_called_with(universe="optcom", contract_name=option_contract)
            assert result == 12345

        # Test futures contract
        futures_contract = "GOLD31-Dec-2023"
        with patch.object(redis_data_handler, 'contract_name_to_balte_id', return_value=67890) as mock_method:
            result = redis_data_handler.get_balte_id(futures_contract)
            mock_method.assert_called_with(universe="mcx_fut_near", contract_name=futures_contract)
            assert result == 67890

        # Test spot contract
        spot_contract = "NIFTY"
        with patch.object(redis_data_handler, 'contract_name_to_balte_id', return_value=1234) as mock_method:
            result = redis_data_handler.get_balte_id(spot_contract)
            mock_method.assert_called_with(universe="mcx_spot", contract_name=spot_contract)
            assert result == 1234

    def test_parse_contract_with_different_months(self):
        """Test parse_contract with different month formats."""
        test_cases = [
            ("NIFTY31-Jan-2023CE24000", ("NIFTY", "31-Jan-2023", "CE", "24000")),
            ("GOLD15-Feb-2024", ("GOLD", "15-Feb-2024", "", "")),
            ("BANKNIFTY28-Dec-2023PE25000", ("BANKNIFTY", "28-Dec-2023", "PE", "25000")),
        ]

        for contract, expected in test_cases:
            result = RedisDataHandlerRunner.parse_contract(contract)
            assert result == expected

    @pytest.mark.asyncio
    async def test_market_termination_checker_with_past_time(self, redis_data_handler):
        """Test market_termination_checker when market closing time is in the past."""
        # Set market closing time to past
        redis_data_handler.market_closing_time = redis_data_handler.get_local_timestamp() - pd.Timedelta(hours=1)

        await redis_data_handler.market_termination_checker()

        assert redis_data_handler.is_market_closed.done()
        assert redis_data_handler.is_market_closed.result() is True
