import pytest
import grpc
import logging
from unittest.mock import Mock, patch
from exchange.helpers.redis_data_handler_components.datasnap_client import (
    DataSnapClient,
)
from toti.rpc import datasnapservice_old_pb2 as datasnap_service_pb2
from exchange.helpers.redis_data_handler_components import datasnap_service_pb2_grpc


@pytest.fixture
def mock_grpc_channel():
    """Fixture to provide a mocked gRPC channel."""
    with patch("grpc.insecure_channel") as mock_channel:
        mock_channel_instance = Mock()
        mock_channel.return_value = mock_channel_instance
        yield mock_channel_instance


@pytest.fixture
def mock_datasnap_stub():
    """Fixture to provide a mocked DataSnap stub."""
    mock_stub = Mock(spec=datasnap_service_pb2_grpc.DataSnapStub)
    return mock_stub


@pytest.fixture
def datasnap_client(mock_datasnap_stub):
    """Fixture to provide a DataSnapClient instance with mocked dependencies."""
    with patch(
        "exchange.helpers.redis_data_handler_components.datasnap_service_pb2_grpc.DataSnapStub"
    ) as mock_stub_class:
        mock_stub_class.return_value = mock_datasnap_stub
        client = DataSnapClient(logger=logging.getLogger())
        client.stub = mock_datasnap_stub
        return client


class TestDataSnapClientInitialization:
    """Test cases for DataSnapClient initialization."""

    def __init__(self):
        self.logger = logging.getLogger()

    def test_init_creates_grpc_channel(self):
        """Test that initialization creates a gRPC channel with correct parameters."""
        with patch("grpc.insecure_channel") as mock_channel, patch(
            "exchange.helpers.redis_data_handler_components.datasnap_service_pb2_grpc.DataSnapStub"
        ):
            DataSnapClient(logger=self.logger)
            mock_channel.assert_called_once_with("localhost:1")

    def test_init_creates_datasnap_stub(self, mock_grpc_channel):
        """Test that initialization creates a DataSnap stub."""
        with patch(
            "exchange.helpers.redis_data_handler_components.datasnap_service_pb2_grpc.DataSnapStub"
        ) as mock_stub_class:
            client = DataSnapClient(logger=self.logger)
            mock_stub_class.assert_called_once_with(mock_grpc_channel)
            assert client.stub == mock_stub_class.return_value

    def test_init_sets_channel_attribute(self, mock_grpc_channel):
        """Test that initialization sets the channel attribute."""
        with patch(
            "exchange.helpers.redis_data_handler_components.datasnap_service_pb2_grpc.DataSnapStub"
        ):
            client = DataSnapClient(logger=self.logger)
            assert client.channel == mock_grpc_channel


class TestDataSnapClientGetTickData:
    """Test cases for the get_tick_data method."""

    def test_get_tick_data_success(self, datasnap_client, mock_datasnap_stub):
        """Test successful get_tick_data call."""
        # Arrange
        expected_response = datasnap_service_pb2.RepeatedArrayReply()
        expected_response.message.extend(["tick1", "tick2", "tick3"])
        mock_datasnap_stub.get_tick_data.return_value = expected_response

        # Act
        result = datasnap_client.get_tick_data(
            "segment1", "2023-01-01T10:00:00", "utf-8"
        )

        # Assert
        assert result == expected_response
        mock_datasnap_stub.get_tick_data.assert_called_once()

        # Verify the request was created correctly
        call_args = mock_datasnap_stub.get_tick_data.call_args[0][0]
        assert call_args.segment == "segment1"
        assert call_args.timestamp == "2023-01-01T10:00:00"
        assert call_args.encoding == "utf-8"

        # Verify logging
        datasnap_client.logger.debug.assert_called()

    def test_get_tick_data_retry_on_error(self, datasnap_client, mock_datasnap_stub):
        """Test get_tick_data retries on gRPC errors and eventually succeeds."""
        # Arrange
        expected_response = datasnap_service_pb2.RepeatedArrayReply()
        expected_response.message.extend(["tick1", "tick2"])

        # First call fails, second call succeeds
        mock_datasnap_stub.get_tick_data.side_effect = [
            grpc.RpcError("Connection failed"),
            expected_response,
        ]

        # Act
        result = datasnap_client.get_tick_data("segment1", "2023-01-01T10:00:00")

        # Assert
        assert result == expected_response
        assert mock_datasnap_stub.get_tick_data.call_count == 2


class TestDataSnapClientGetLatestSecond:
    """Test cases for the get_latest_second method."""

    def test_get_latest_second_success(self, datasnap_client, mock_datasnap_stub):
        """Test successful get_latest_second call."""
        # Arrange
        expected_response = datasnap_service_pb2.TimestampResponse()
        expected_response.timestamp = "2023-01-01T10:00:00"
        mock_datasnap_stub.get_latest_second.return_value = expected_response

        # Act
        result = datasnap_client.get_latest_second("segment1")

        # Assert
        assert result == expected_response
        assert result.timestamp == "2023-01-01T10:00:00"
        mock_datasnap_stub.get_latest_second.assert_called_once()

        # Verify the request was created correctly
        call_args = mock_datasnap_stub.get_latest_second.call_args[0][0]
        assert call_args.segment == "segment1"

        # Verify logging
        datasnap_client.logger.debug.assert_called()

    def test_get_latest_second_retry_on_error(
        self, datasnap_client, mock_datasnap_stub
    ):
        """Test get_latest_second retries on gRPC errors and eventually succeeds."""
        # Arrange
        expected_response = datasnap_service_pb2.TimestampResponse()
        expected_response.timestamp = "2023-01-01T10:00:00"

        # First call fails, second call succeeds
        mock_datasnap_stub.get_latest_second.side_effect = [
            grpc.RpcError("Connection failed"),
            expected_response,
        ]

        # Act
        result = datasnap_client.get_latest_second("segment1")

        # Assert
        assert result == expected_response
        assert mock_datasnap_stub.get_latest_second.call_count == 2


class TestDataSnapClientClose:
    """Test cases for the close method."""

    def test_close_calls_channel_close(self, datasnap_client, mock_grpc_channel):
        """Test that close method calls channel.close()."""
        # Act
        datasnap_client.close()

        # Assert
        mock_grpc_channel.close.assert_called_once()
        datasnap_client.logger.info.assert_called()

    def test_close_multiple_calls(self, datasnap_client, mock_grpc_channel):
        """Test that multiple close calls work without error."""
        # Act
        datasnap_client.close()
        datasnap_client.close()

        # Assert
        assert mock_grpc_channel.close.call_count == 2
