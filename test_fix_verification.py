#!/usr/bin/env python3
"""
Quick verification script to test the DataSnapClient test fixes.
"""

import sys
import os
sys.path.insert(0, '/opt/build/src')

from unittest.mock import Mock
from toti.rpc import datasnapservice_old_pb2 as datasnap_service_pb2

def test_mock_setup():
    """Test that our mock setup works correctly."""
    print("Testing mock setup...")
    
    # Create mock stub like in our test
    mock_stub = Mock()
    mock_stub.get_tick_data = Mock()
    mock_stub.get_latest_second = Mock()
    
    # Test that we can set return values
    expected_response = datasnap_service_pb2.RepeatedArrayReply()
    expected_response.message.extend(["tick1", "tick2", "tick3"])
    mock_stub.get_tick_data.return_value = expected_response
    
    # Test that we can call the mock
    result = mock_stub.get_tick_data("test_request")
    
    print(f"✓ Mock setup works: {result == expected_response}")
    print(f"✓ Mock was called: {mock_stub.get_tick_data.called}")
    
    return True

def test_protobuf_messages():
    """Test that protobuf messages work correctly."""
    print("Testing protobuf messages...")
    
    # Test RepeatedArrayReply
    response = datasnap_service_pb2.RepeatedArrayReply()
    response.message.extend(["tick1", "tick2", "tick3"])
    print(f"✓ RepeatedArrayReply created: {len(response.message)} messages")
    
    # Test TimestampResponse
    timestamp_response = datasnap_service_pb2.TimestampResponse()
    timestamp_response.timestamp = "2023-01-01T10:00:00"
    print(f"✓ TimestampResponse created: {timestamp_response.timestamp}")
    
    # Test DataSnapRequest
    request = datasnap_service_pb2.DataSnapRequest(
        segment="segment1", 
        timestamp="2023-01-01T10:00:00", 
        encoding="utf-8"
    )
    print(f"✓ DataSnapRequest created: {request.segment}")
    
    # Test TimeStampRequest
    ts_request = datasnap_service_pb2.TimeStampRequest(segment="segment1")
    print(f"✓ TimeStampRequest created: {ts_request.segment}")
    
    return True

if __name__ == "__main__":
    print("Verifying DataSnapClient test fixes...")
    print("=" * 50)
    
    try:
        test_mock_setup()
        print()
        test_protobuf_messages()
        print()
        print("✅ All verification tests passed!")
        print("The test fixes should work correctly.")
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
